// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Suggestions component should take suggestion component snapshot 1`] = `
<div>
  <main
    class="sc-1ehavxz-0 dDyhBJ"
  >
    <div
      class="sc-h70dbj-0 fQmYvl row expanded space-between align-center marginBottom-2"
    >
      <div
        class="sc-w8e7z6-2"
      >
        <h3
          class="sc-1ntgl3l-0 kcLBOi sc-w8e7z6-0 ibnmt"
          textalign="initial"
          textcolor="inherit"
          textvariant="h3"
          textweight="Medium"
        >
          Suggestions
        </h3>
      </div>
      <button
        class="sc-1k2druj-0 eLgfbc sc-w8e7z6-1"
        edges="square"
        type="button"
        variant="solid"
      >
        <span
          class="sc-1ntgl3l-0 eOsHHD"
          textalign="initial"
          textcolor="inherit"
          textvariant="button1"
        >
          Add Suggestion
        </span>
      </button>
    </div>
    <div
      class="sc-1etsj2e-0 gMXChf sc-tkiih6-20 sc-14cgrrd-6 iSFEzl eQVXXE"
      id="medly-modal"
      open=""
    >
      <div
        class="sc-bu1pt3-0 kLJgCq"
        id="medly-modal-popup"
        open=""
      >
        <svg
          class="sc-1pf3x8a-0 eHKFnt sc-1jyrp7a-0 gPLpLo"
          fill="none"
          height="1em"
          id="medly-modal-close-button"
          title="medly-modal-close-icon"
          viewBox="0 0 24 24"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18.3 5.71a.996.996 0 0 0-1.41 0L12 10.59 7.11 5.7A.996.996 0 1 0 5.7 7.11L10.59 12 5.7 16.89a.996.996 0 1 0 1.41 1.41L12 13.41l4.89 4.89a.996.996 0 1 0 1.41-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z"
            fill="#000"
            fill-opacity=".54"
          />
        </svg>
        <div
          class="sc-1etsj2e-1 jRtjKC"
          headerheight="0"
          id="medly-modal-inner-container"
        >
          <div
            class="sc-1whgp8r-0 cmHeGg"
            id="medly-modal-header"
            scrollstate="[object Object]"
          >
            <h2
              class="sc-1ntgl3l-0 hJdvRC sc-tkiih6-27 ebKlKN"
              textalign="initial"
              textcolor="inherit"
              textvariant="h2"
            >
              Suggestion
            </h2>
          </div>
          <div
            class="sc-1dfqrlx-0 bmLVFj sc-tkiih6-25"
            headerheight="0"
            id="medly-modal-content"
            scrollstate="[object Object]"
          >
            <div
              class="sc-14cgrrd-5 kUNNtK"
            >
              <div
                class="sc-14cgrrd-7 kVFNMe"
              >
                <span
                  class="sc-1ntgl3l-0 gPMgWC sc-14cgrrd-9 iIeVJE"
                  textalign="initial"
                  textcolor="inherit"
                  textweight="Medium"
                >
                  Date: 
                  <span
                    class="sc-1ntgl3l-0 hGagSU sc-14cgrrd-10 wcsSc"
                    textalign="initial"
                    textcolor="inherit"
                  >
                    10/05/2024
                  </span>
                </span>
              </div>
              <div
                class="sc-14cgrrd-7 kVFNMe"
              >
                <h4
                  class="sc-1ntgl3l-0 jPeJxZ sc-14cgrrd-9 iIeVJE"
                  textalign="initial"
                  textcolor="inherit"
                  textvariant="h4"
                  textweight="Medium"
                >
                  Suggested By: 
                  <span
                    class="sc-1ntgl3l-0 hGagSU sc-14cgrrd-10 wcsSc"
                    textalign="initial"
                    textcolor="inherit"
                  >
                    Test User (C0001)
                  </span>
                </h4>
              </div>
              <div
                class="sc-14cgrrd-8 iEJfpM"
              >
                <h4
                  class="sc-1ntgl3l-0 jPeJxZ sc-14cgrrd-9 iIeVJE"
                  textalign="initial"
                  textcolor="inherit"
                  textvariant="h4"
                  textweight="Medium"
                >
                  Suggestion:
                </h4>
                <span
                  class="sc-1ntgl3l-0 hGagSU sc-14cgrrd-12 fKkWfv"
                  textalign="initial"
                  textcolor="inherit"
                >
                  <p>
                    gfhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh
                  </p>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="sc-14cgrrd-14 bIngPd"
    >
      <div
        class="sc-14cgrrd-15 cbmTZz"
      >
        <div
          class="sc-1hcsztp-0 fKSUGy sc-tkiih6-13 RzGMw"
          id="progress-wrapper"
          minwidth="25rem"
          variant="outlined"
        >
          <div
            class="sc-17d4s39-0 UfceN"
            id="progress-input-wrapper"
            minwidth="25rem"
          >
            <div
              class="sc-19px6zp-0 BrSGZ"
              filled="[object Object]"
              fusion="[object Object]"
              minrows="1"
              outlined="[object Object]"
              textvariant="[object Object]"
              variant="outlined"
            >
              <div
                class="sc-lhr232-0 btNOKw"
                variant="outlined"
              >
                <input
                  aria-describedby="progress-helper-text"
                  autocomplete="off"
                  class="sc-hb99wz-0 jaEbhp"
                  data-testid="progressDropdown"
                  errortext=""
                  id="progress-input"
                  inputmode="none"
                  inputsize="M"
                  placeholder="Select Progress"
                  type="text"
                  value="All"
                  variant="outlined"
                />
                <label
                  class="sc-1du51u0-0 eMZrST"
                  for="progress-input"
                  inputwidth="0"
                  variant="outlined"
                >
                  Progress
                </label>
              </div>
              <svg
                class="sc-1pf3x8a-0 klDMsE sc-dynlvq-0 bdmviR"
                fill="none"
                height="1em"
                viewBox="0 0 24 24"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12.792 14.97a1 1 0 0 1-1.584 0l-3.165-4.11a1 1 0 0 1 .793-1.61h6.328a1 1 0 0 1 .793 1.61l-3.165 4.11z"
                  fill="#000"
                />
              </svg>
            </div>
            
          </div>
        </div>
      </div>
    </div>
    <div
      aria-label="Closed style tabs"
      class="sc-13bppbd-0 sc-tkiih6-28 sc-14cgrrd-0 dErHOx egGSkT"
      id="medly-tabs"
    >
      <div
        class="sc-12mpb33-0 dYQjJT"
        id="medly-tabs-list"
        tabsize="M"
        variant="outlined"
      >
        <button
          aria-controls="panel-submittedSuggestion"
          aria-selected="true"
          bgcolor="[object Object]"
          bordercolor="[object Object]"
          class="sc-1kcg570-4 ewhzXK"
          countbgcolor="[object Object]"
          countborderradius="2.5rem"
          countcolor="#ffffff"
          data-testid="submitted"
          helpertextcolor="[object Object]"
          iconbgcolor="[object Object]"
          iconcolor="[object Object]"
          id="submittedSuggestion"
          label="[object Object]"
          labelcolor="[object Object]"
          role="tab"
          solid="[object Object]"
          tabbackground="WHITE"
          tabindex="0"
          tabsize="M"
          totaltabs="2"
          type="button"
          variant="outlined"
        >
          <svg
            class="sc-1pf3x8a-0 kdcvez"
            fill="none"
            height="1em"
            viewBox="0 0 24 24"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.296 5.704c0 .56.45 1 1 1h5.59l-10.89 10.89a.996.996 0 1 0 1.41 1.41l10.89-10.89v5.59c0 .55.45 1 1 1s1-.45 1-1v-8c0-.55-.45-1-1-1h-8c-.55 0-1 .45-1 1z"
              fill="#000"
              fill-opacity=".54"
            />
          </svg>
          <div
            class="sc-1kcg570-5 kFiIrt"
          >
            <div
              class="sc-1kcg570-6 jobpmu"
            >
              <span
                class="sc-1ntgl3l-0 hGagSU sc-1kcg570-1 jILKEJ"
                id="submittedSuggestion-label"
                tabsize="M"
                textalign="initial"
                textcolor="inherit"
              >
                Submitted
              </span>
            </div>
          </div>
        </button>
        <button
          aria-controls="panel-receivedSuggestion"
          aria-selected="false"
          bgcolor="[object Object]"
          bordercolor="[object Object]"
          class="sc-1kcg570-4 fpIKmc"
          countbgcolor="[object Object]"
          countborderradius="2.5rem"
          countcolor="#ffffff"
          data-testid="received"
          helpertextcolor="[object Object]"
          iconbgcolor="[object Object]"
          iconcolor="[object Object]"
          id="receivedSuggestion"
          label="[object Object]"
          labelcolor="[object Object]"
          role="tab"
          solid="[object Object]"
          tabbackground="WHITE"
          tabindex="-1"
          tabsize="M"
          totaltabs="2"
          type="button"
          variant="outlined"
        >
          <svg
            class="sc-1pf3x8a-0 kdcvez"
            fill="none"
            height="1em"
            viewBox="0 0 24 24"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.008 5.001a.996.996 0 0 0-1.41 0l-10.89 10.88v-5.59c0-.55-.45-1-1-1s-1 .45-1 1v8c0 .55.45 1 1 1h8c.55 0 1-.45 1-1s-.45-1-1-1h-5.59l10.89-10.89c.38-.38.38-1.02 0-1.4z"
              fill="#000"
              fill-opacity=".54"
            />
          </svg>
          <div
            class="sc-1kcg570-5 kFiIrt"
          >
            <div
              class="sc-1kcg570-6 jobpmu"
            >
              <span
                class="sc-1ntgl3l-0 hGagSU sc-1kcg570-1 jILKEJ"
                id="receivedSuggestion-label"
                tabsize="M"
                textalign="initial"
                textcolor="inherit"
              >
                Received
              </span>
            </div>
          </div>
        </button>
      </div>
      <div
        aria-labelledby="submittedSuggestion"
        class="sc-x0gfjw-0 bLZBSc"
        id="medly-tabs-panel-submittedSuggestion"
        role="tabpanel"
        tabindex="0"
      />
    </div>
    <div
      class="sc-14cgrrd-1 ccgMDN"
    >
      <div
        class="sc-8tbhpn-3"
      >
        <div
          class="sc-1msmxxr-0 iWEzjW"
        />
        <table
          actions=""
          class="sc-1msmxxr-1 jwFsPF sc-8tbhpn-2 jSoPjS"
          columns="[object Object],[object Object],[object Object],[object Object],[object Object]"
          defaultactivepage="1"
          defaultsortorder="desc"
          itemsperpage="10"
          rowclickdisablekey=""
          tabindex="-1"
          totalitems="0"
        >
          <thead
            class="sc-uxzsxp-0 haNltq"
          >
            <tr
              class="sc-1jr91bg-0 hJzMcO"
              gridtemplatecolumns=" minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr)"
              style="grid-template-columns: minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr);"
            >
              <th
                class="sc-hjmm4m-0 iOyNiA"
                tablesize="M"
              >
                <button
                  class="sc-hjmm4m-2 kpjakb"
                  tablesize="M"
                >
                  <span
                    class="sc-1ntgl3l-0 ghpJwn"
                    textalign="initial"
                    textcolor="inherit"
                  >
                    Date
                  </span>
                  <svg
                    class="sc-1pf3x8a-0 bMQNVa"
                    fill="none"
                    height="1em"
                    viewBox="0 0 24 24"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M11.229 6.935a1 1 0 0 1 1.543 0l2.208 2.679a1 1 0 0 1-.77 1.636H9.79a1 1 0 0 1-.77-1.636l2.209-2.679zM12.771 17.065a1 1 0 0 1-1.543 0L9.02 14.386a1 1 0 0 1 .772-1.636h4.418a1 1 0 0 1 .771 1.636l-2.209 2.679z"
                      fill="#435465"
                    />
                  </svg>
                </button>
                <span
                  class="sc-hjmm4m-1 cuEDpa"
                />
              </th>
              <th
                class="sc-hjmm4m-0 iOyNiA"
                tablesize="M"
              >
                <button
                  class="sc-hjmm4m-2 kpjakb"
                  tablesize="M"
                >
                  <span
                    class="sc-1ntgl3l-0 ghpJwn"
                    textalign="initial"
                    textcolor="inherit"
                  >
                    Suggestion
                  </span>
                </button>
                <span
                  class="sc-hjmm4m-1 cuEDpa"
                />
              </th>
              <th
                class="sc-hjmm4m-0 iOyNiA"
                tablesize="M"
              >
                <button
                  class="sc-hjmm4m-2 kpjakb"
                  tablesize="M"
                >
                  <span
                    class="sc-1ntgl3l-0 ghpJwn"
                    textalign="initial"
                    textcolor="inherit"
                  >
                    Status
                  </span>
                </button>
                <span
                  class="sc-hjmm4m-1 cuEDpa"
                />
              </th>
              <th
                class="sc-hjmm4m-0 iOyNiA"
                tablesize="M"
              >
                <button
                  class="sc-hjmm4m-2 kpjakb"
                  tablesize="M"
                >
                  <span
                    class="sc-1ntgl3l-0 ghpJwn"
                    textalign="initial"
                    textcolor="inherit"
                  >
                    Progress
                  </span>
                </button>
                <span
                  class="sc-hjmm4m-1 cuEDpa"
                />
              </th>
              <th
                class="sc-hjmm4m-0 iOyNiA"
                tablesize="M"
              >
                <button
                  class="sc-hjmm4m-2 kpjakb"
                  tablesize="M"
                >
                  <span
                    class="sc-1ntgl3l-0 ghpJwn"
                    textalign="initial"
                    textcolor="inherit"
                  >
                    Action
                  </span>
                </button>
                <span
                  class="sc-hjmm4m-1 cuEDpa"
                />
              </th>
            </tr>
          </thead>
          <tbody
            class="sc-c7cniy-0 kShdfA"
          >
            <tr
              class="sc-1yxujaa-0 bgyIwA"
              gridtemplatecolumns=" minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr)"
              style="grid-template-columns: minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr);"
              tabindex="0"
            >
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
            </tr>
            <tr
              class="sc-1yxujaa-0 bgyIwA"
              gridtemplatecolumns=" minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr)"
              style="grid-template-columns: minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr);"
              tabindex="1"
            >
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
            </tr>
            <tr
              class="sc-1yxujaa-0 bgyIwA"
              gridtemplatecolumns=" minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr)"
              style="grid-template-columns: minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr);"
              tabindex="2"
            >
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
            </tr>
            <tr
              class="sc-1yxujaa-0 bgyIwA"
              gridtemplatecolumns=" minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr)"
              style="grid-template-columns: minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr);"
              tabindex="3"
            >
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
            </tr>
            <tr
              class="sc-1yxujaa-0 bgyIwA"
              gridtemplatecolumns=" minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr)"
              style="grid-template-columns: minmax(100px, 1fr) minmax(100px, 2fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(100px, 1fr);"
              tabindex="4"
            >
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
              <td
                class="sc-1e51eaa-0 hFOqbY"
                tablesize="M"
              >
                <div
                  class="sc-15vbyef-0 bHPECM"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </main>
</div>
`;
