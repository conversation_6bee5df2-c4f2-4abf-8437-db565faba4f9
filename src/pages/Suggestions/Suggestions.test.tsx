import MockAdapter from 'axios-mock-adapter';
import { apiInstance } from '@utils';
import { Suggestions } from './Suggestions';
import { routeConstants } from '@constants';
import { RTKRender, cleanup, fireEvent, screen, waitFor } from '@utils/test-utils';
import { notificationCountData, suggestionMockData } from '@mocks/suggestions';
import * as reactRouterDom from 'react-router-dom';
import { userMockState } from '@mocks/userMockState';

const mock = new MockAdapter(apiInstance, { onNoMatch: 'throwException' });

const mockedNavigate = jest.fn();

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: () => mockedNavigate,
    useLocation: jest.fn()
}));

const useLocationMock = jest.spyOn(reactRouterDom, 'useLocation');
let mockedState = { ...suggestionMockData.mockState, action: '' };
const originalScrollIntoWindow = window.HTMLElement.prototype.scrollIntoView;

// Mock sessionStorage
const mockSessionStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
Object.defineProperty(window, 'sessionStorage', { value: mockSessionStorage });

beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock sessionStorage with proper handling for different keys
    mockSessionStorage.getItem.mockImplementation(key => {
        if (key === 'myTab') return 'submittedSuggestion';
        if (key === 'tooltips') return null; // Return null for tooltips to avoid JSON parse error
        return null;
    });

    useLocationMock.mockReturnValue({
        ...suggestionMockData.locationMockData,
        pathname: `${routeConstants.suggestionBox}/1`,
        state: mockedState
    });

    // Set up the correct URLs that match what the component actually requests
    const submittedUrl =
        'https://dummy-api.example.com/api/suggestion/?organisationId=1&suggestionById=72&reviewCycleId=&isDraft=true,false&page=1&progressIds=-99&limit=10&sortBy=dateDesc';
    const receivedUrl =
        'https://dummy-api.example.com/api/suggestion/?organisationId=1&suggestionById=-99&reviewCycleId=&isDraft=false&page=1&progressIds=-99&limit=10&sortBy=dateDesc';
    const progressListUrl = 'https://dummy-api.example.com/api/suggestion/progress-list/';
    const pendingCountUrl = 'https://dummy-api.example.com/api/suggestion/pending-count/?organisationId=1&reviewCycleId=';

    // Mock the API responses
    mock.onGet(submittedUrl).reply(200, suggestionMockData.data);
    mock.onGet(receivedUrl).reply(200, suggestionMockData.data);
    mock.onGet(progressListUrl).reply(200, suggestionMockData.progressList);
    mock.onGet(pendingCountUrl).reply(200, notificationCountData.data);

    window.HTMLElement.prototype.scrollIntoView = jest.fn();
});

afterEach(() => {
    cleanup();
    mock.reset();
    window.HTMLElement.prototype.scrollIntoView = originalScrollIntoWindow;
});

describe('Suggestions component', () => {
    it('should take suggestion component snapshot', () => {
        const { container } = RTKRender(<Suggestions />);
        expect(container).toMatchSnapshot();
    });

    it('should sorting work propely', async () => {
        RTKRender(<Suggestions />);
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });
        const spanElement = screen.getByText('Date');
        fireEvent.click(spanElement);
        await waitFor(() => expect(screen.getAllByText('View')[0]).toBeInTheDocument(), { timeout: 5000 });
    });

    it('should open and close view suggestion popup when clicked on `View` Action', async () => {
        mockedState = { ...suggestionMockData.mockState, action: 'View' };
        useLocationMock.mockReturnValue({
            ...suggestionMockData.locationMockData,
            pathname: `${routeConstants.suggestionBox}/1`,
            state: mockedState
        });
        RTKRender(<Suggestions />);
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });
        fireEvent.click(screen.getAllByText('View')[0]);
        expect(await screen.findByText('Test User (C0001)')).toBeInTheDocument();
        fireEvent.click(screen.getByTitle('medly-modal-close-icon'));
        await waitFor(() => expect(screen.queryByText('Test User (C0001)')).not.toBeInTheDocument(), { timeout: 5000 });
    });

    it('should add suggestion button rendered properly and must be clickable', async () => {
        RTKRender(<Suggestions />, {
            initialEntries: [`${routeConstants.suggestionBox}/1`]
        });
        const button = screen.getByRole('button', { name: 'Add Suggestion' });
        fireEvent.click(button);
        expect(mockedNavigate).toHaveBeenCalledWith(`${routeConstants.suggestionBox}/1/add-suggestion`, {
            state: { action: 'Add' }
        });
    });

    it('should progress update work properly', async () => {
        const progressUpdateUrl = 'https://dummy-api.example.com/api/suggestion/progress/189';
        const pendingCountUrl = 'https://dummy-api.example.com/api/suggestion/pending-count/?organisationId=1&reviewCycleId=';

        mock.onPatch(progressUpdateUrl).reply(200, {});
        mock.onGet(pendingCountUrl).reply(200, notificationCountData.notPendingData);

        RTKRender(<Suggestions />, {
            initialState: userMockState
        });

        // Wait for initial load
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });

        // Click on received tab
        fireEvent.click(screen.getByTestId('received'));

        // Wait for received data to load
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });

        expect(screen.getByText('2')).toBeInTheDocument();
        fireEvent.click(screen.getAllByText('View')[0]);
        fireEvent.change(screen.getByTestId('progress'), { target: { value: 'Completed' } });
        fireEvent.click(screen.getByText('Save'));
        expect(screen.getByText('1')).toBeInTheDocument();
        expect(await screen.findByText('Suggestion progress updated successfully')).toBeInTheDocument();
    }, 10000);

    it('should check if progress update API error is handled properly', async () => {
        mockedState = { ...suggestionMockData.mockState, suggestedByFirstName: '' };
        useLocationMock.mockReturnValue({
            ...suggestionMockData.locationMockData,
            pathname: `${routeConstants.suggestionBox}/1`,
            state: mockedState
        });

        const progressUpdateUrl = 'https://dummy-api.example.com/api/suggestion/progress/189';

        mock.onPatch(progressUpdateUrl).reply(404, {
            errorMessage: 'something went wrong'
        });

        RTKRender(<Suggestions />, {
            initialState: userMockState
        });

        // Wait for initial load
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });

        // Click on received tab
        fireEvent.click(screen.getByTestId('received'));

        // Wait for received data to load
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });

        fireEvent.click(screen.getAllByText('View')[0]);
        fireEvent.change(screen.getByTestId('progress'), { target: { value: 'Completed' } });
        fireEvent.click(screen.getByText('Save'));
        expect(await screen.findByText('something went wrong')).toBeInTheDocument();
    }, 10000);

    it('should check if filter works properly', async () => {
        const progressFilterAllUrl =
            'https://dummy-api.example.com/api/suggestion/?organisationId=1&suggestionById=-99&reviewCycleId=&isDraft=false&page=1&progressIds=-99&limit=10&sortBy=dateDesc';
        const progressFilterPendingUrl =
            'https://dummy-api.example.com/api/suggestion/?organisationId=1&suggestionById=-99&reviewCycleId=&isDraft=false&page=1&progressIds=1&limit=10&sortBy=dateDesc';

        mock.onGet(progressFilterAllUrl).reply(200, suggestionMockData.progressFilterAllData);
        mock.onGet(progressFilterPendingUrl).reply(200, suggestionMockData.progressFilterPendingData);

        RTKRender(<Suggestions />, {
            initialState: userMockState
        });

        // Wait for initial load
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });

        // Click on received tab
        fireEvent.click(screen.getByTestId('received'));

        // Wait for received data to load
        await waitFor(() => screen.findAllByText('View'), { timeout: 5000 });

        expect(screen.getAllByText('Completed').length).toEqual(3);
        const progressDropdown = screen.getByTestId('progressDropdown');

        expect(progressDropdown).toBeInTheDocument();
        fireEvent.click(progressDropdown);

        const PendingStatus = await screen.findAllByText('Pending');
        fireEvent.click(PendingStatus[0]);

        expect((await screen.findAllByText('Pending')).length).toEqual(suggestionMockData.progressFilterPendingData.totalSuggestions);
    }, 10000);
});
